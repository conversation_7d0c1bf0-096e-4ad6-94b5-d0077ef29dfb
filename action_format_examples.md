# Action 格式使用说明

## 概述

`do_job.py` 现在支持新的中文 action 格式：`应用名.方法名`，使用点号分割应用和方法，更加直观易懂。

## 新格式语法

```
--action 应用名.方法名
```

## 重要变更

1. **模块重命名**：`task_operations_tianmao.py` 已重命名为 `天猫.py`
2. **中文格式**：支持中文应用名和方法名，如 `天猫.福气任务`
3. **自动打开应用**：新增 `open_app()` 通用方法，支持自动打开指定应用
4. **简化参数**：`execute_action()` 方法简化为只接收 action 参数

## 支持的 Action 示例

### 天猫应用任务

```bash
# 执行福气任务（自动打开应用）
python do_job.py --action 天猫.福气任务 --need_open_app --is_clone

# 执行测试任务
python do_job.py --action 天猫.测试任务

# 执行快手任务
python do_job.py --action 天猫.快手任务
```

### 花园应用任务

```bash
# 执行花园任务（自动打开应用）
python do_job.py --action 花园.花园任务 --need_open_app

# 执行自动拖拽任务
python do_job.py --action 花园.自动拖拽
```

### 应用名和方法名映射

| 中文应用名 | 实际模块 | 中文方法名 | 实际方法名 |
|-----------|----------|-----------|-----------|
| 天猫 | 天猫.py | 福气任务 | do_job |
| 天猫 | 天猫.py | 测试任务 | do_test |
| 天猫 | 天猫.py | 快手任务 | do_ks |
| 花园 | task_tianmao_huayuan.py | 花园任务 | do_huayuan |
| 花园 | task_tianmao_huayuan.py | 自动拖拽 | auto_drag_matching_elements_loop |

## 自动打开应用功能

新增的 `open_app()` 方法可以自动打开指定应用：

```bash
# 自动打开天猫应用并执行福气任务
python do_job.py --action 天猫.福气任务 --need_open_app

# 自动打开天猫分身应用并执行任务
python do_job.py --action 天猫.福气任务 --need_open_app --is_clone
```

### 应用打开逻辑

- 自动回到桌面
- 查找应用图标（支持原版和分身）
- 按X坐标排序（左侧为原版，右侧为分身）
- 点击对应图标并等待应用启动

## 兼容性

### 向后兼容

旧格式仍然支持，会自动映射到新格式：

```bash
# 旧格式（仍然支持）
python do_job.py --action do_job
# 自动映射为：天猫.福气任务

# 旧模块格式（仍然支持）
python do_job.py --action task_operations_tianmao.do_job
# 直接使用旧模块
```

### 参数简化

新版本简化了参数传递：

- 所有方法都使用无参数调用，依赖方法内部的默认参数
- `need_open_app` 和 `is_clone` 参数在 main 函数中处理，用于控制是否自动打开应用

## 错误处理

系统会提供详细的错误信息：

```bash
# 无效应用名
python do_job.py --action 无效应用.无效方法
# 输出: [错误] 未知的应用名称: '无效应用'
# 输出: [提示] 支持的应用名称: ['天猫', '花园', 'task_operations_tianmao', 'task_tianmao_huayuan']

# 无效方法名
python do_job.py --action 天猫.无效方法
# 输出: [错误] 未知的方法名称: '无效方法'
# 输出: [提示] 支持的方法名称: ['福气任务', '测试任务', '快手任务', '花园任务', '自动拖拽', ...]
```

## 完整示例

```bash
# 示例1: 使用新中文格式执行天猫福气任务
python do_job.py \
  --sb_name "mix2_zi" \
  --sb_url "192.168.123.29:5555" \
  --action "天猫.福气任务" \
  --need_open_app \
  --is_clone

# 示例2: 执行花园自动拖拽任务
python do_job.py \
  --sb_name "k80pro" \
  --sb_url "192.168.123.39:36123" \
  --action "花园.自动拖拽"

# 示例3: 兼容旧格式
python do_job.py --action "do_test"

# 示例4: 自动打开应用执行任务
python do_job.py \
  --action "天猫.测试任务" \
  --need_open_app
```

## 添加新应用和方法

要添加新的应用和方法：

1. **添加新模块文件**（如 `新应用.py`）
2. **在模块中定义方法**
3. **更新 `do_job.py` 中的映射表**：
   - 在 `app_to_module` 中添加应用名映射
   - 在 `method_mapping` 中添加方法名映射
   - 在 `app_name_mapping` 中添加应用打开映射
4. **使用新格式调用**：`--action "新应用.新方法"`

### 映射表示例

```python
# 在 execute_action 函数中
app_to_module = {
    '天猫': '天猫',
    '花园': 'task_tianmao_huayuan',
    '新应用': '新应用',  # 添加新应用
}

method_mapping = {
    '福气任务': 'do_job',
    '新任务': 'new_method',  # 添加新方法
}
```
