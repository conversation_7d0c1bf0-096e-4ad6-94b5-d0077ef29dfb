# Action 格式使用说明

## 概述

`do_job.py` 现在支持新的中文 action 格式：`应用名.方法名`，使用点号分割应用和方法，更加直观易懂。

## 新格式语法

```
--action 应用名.方法名
```

## 重要变更

1. **模块重命名**：`task_operations_tianmao.py` 已重命名为 `天猫.py`
2. **中文格式**：支持中文应用名和方法名，如 `天猫.福气任务`
3. **自动打开应用**：新增 `open_app()` 通用方法，支持自动打开指定应用
4. **简化参数**：`execute_action()` 方法简化为只接收 action 参数
5. **🎉 无需维护映射表**：直接在 Python 文件中使用中文方法名，无需维护方法名映射表

## 支持的 Action 示例

### 天猫应用任务

```bash
# 执行福气任务（自动打开应用）
python do_job.py --action 天猫.福气任务 --need_open_app --is_clone

# 执行测试任务
python do_job.py --action 天猫.测试任务

# 执行快手任务
python do_job.py --action 天猫.快手任务
```

### 花园应用任务

```bash
# 执行花园任务（自动打开应用）
python do_job.py --action 花园.花园任务 --need_open_app

# 执行自动拖拽任务
python do_job.py --action 花园.自动拖拽
```

### 应用名和方法名对应

| 中文应用名 | 实际模块 | 中文方法名 | 说明 |
|-----------|----------|-----------|------|
| 天猫 | 天猫.py | 福气任务 | 执行天猫福气任务 |
| 天猫 | 天猫.py | 测试任务 | 执行测试任务 |
| 天猫 | 天猫.py | 快手任务 | 执行快手任务 |
| 花园 | task_tianmao_huayuan.py | 花园任务 | 执行花园任务 |
| 花园 | task_tianmao_huayuan.py | 自动拖拽 | 执行自动拖拽任务 |

**注意**：现在直接使用中文方法名，无需维护映射表！

## 自动打开应用功能

新增的 `open_app()` 方法可以自动打开指定应用：

```bash
# 自动打开天猫应用并执行福气任务
python do_job.py --action 天猫.福气任务 --need_open_app

# 自动打开天猫分身应用并执行任务
python do_job.py --action 天猫.福气任务 --need_open_app --is_clone
```

### 应用打开逻辑

- 自动回到桌面
- 查找应用图标（支持原版和分身）
- 按X坐标排序（左侧为原版，右侧为分身）
- 点击对应图标并等待应用启动

## 兼容性

### 向后兼容

旧格式仍然支持，会自动映射到新格式：

```bash
# 旧格式（仍然支持）
python do_job.py --action do_job
# 自动映射为：天猫.福气任务

# 旧模块格式（仍然支持）
python do_job.py --action task_operations_tianmao.do_job
# 直接使用旧模块
```

### 参数简化

新版本简化了参数传递：

- 所有方法都使用无参数调用，依赖方法内部的默认参数
- `need_open_app` 和 `is_clone` 参数在 main 函数中处理，用于控制是否自动打开应用

## 错误处理

系统会提供详细的错误信息：

```bash
# 无效应用名
python do_job.py --action 无效应用.无效方法
# 输出: [错误] 未知的应用名称: '无效应用'
# 输出: [提示] 支持的应用名称: ['天猫', '花园', 'task_operations_tianmao', 'task_tianmao_huayuan']

# 无效方法名
python do_job.py --action 天猫.无效方法
# 输出: [错误] 未知的方法名称: '无效方法'
# 输出: [提示] 支持的方法名称: ['福气任务', '测试任务', '快手任务', '花园任务', '自动拖拽', ...]
```

## 完整示例

```bash
# 示例1: 使用新中文格式执行天猫福气任务
python do_job.py \
  --sb_name "mix2_zi" \
  --sb_url "192.168.123.29:5555" \
  --action "天猫.福气任务" \
  --need_open_app \
  --is_clone

# 示例2: 执行花园自动拖拽任务
python do_job.py \
  --sb_name "k80pro" \
  --sb_url "192.168.123.39:36123" \
  --action "花园.自动拖拽"

# 示例3: 兼容旧格式
python do_job.py --action "do_test"

# 示例4: 自动打开应用执行任务
python do_job.py \
  --action "天猫.测试任务" \
  --need_open_app
```

## 添加新应用和方法

现在添加新应用和方法变得非常简单，无需维护映射表：

### 方法1：添加新应用

1. **创建新模块文件**（如 `抖音.py`）
2. **在模块中直接定义中文方法名**：
   ```python
   # 抖音.py
   def 刷视频():
       """执行刷视频任务"""
       # 具体实现
       pass

   def 点赞任务():
       """执行点赞任务"""
       # 具体实现
       pass
   ```
3. **更新 `do_job.py` 中的应用映射**：
   ```python
   app_to_module = {
       '天猫': '天猫',
       '花园': 'task_tianmao_huayuan',
       '抖音': '抖音',  # 添加新应用
   }
   ```
4. **直接使用**：`--action "抖音.刷视频"`

### 方法2：在现有模块中添加新方法

直接在现有模块文件中添加中文方法名：

```python
# 在 天猫.py 中添加
def 新任务():
    """执行新任务"""
    # 具体实现
    pass
```

然后直接使用：`--action "天猫.新任务"`

### 优势

- ✅ **无需维护映射表**：直接使用中文方法名
- ✅ **代码更清晰**：方法名直观易懂
- ✅ **维护简单**：添加方法只需在对应模块中定义
- ✅ **自动发现**：系统自动识别新的中文方法名
