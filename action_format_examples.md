# Action 格式使用说明

## 概述

`do_job.py` 现在支持新的 action 格式：`模块名.方法名`，使用点号分割模块和方法。

## 新格式语法

```
--action 模块名.方法名
```

## 支持的 Action 示例

### task_operations_tianmao 模块

```bash
# 执行主要任务（支持 need_open_app 和 is_clone 参数）
python do_job.py --action task_operations_tianmao.do_job --need_open_app --is_clone

# 执行测试任务
python do_job.py --action task_operations_tianmao.do_test

# 执行快手任务
python do_job.py --action task_operations_tianmao.do_ks
```

### task_tianmao_huayuan 模块

```bash
# 执行花园自动拖拽任务
python do_job.py --action task_tianmao_huayuan.auto_drag_matching_elements_loop

# 执行花园任务（支持 need_open_app 和 is_clone 参数）
python do_job.py --action task_tianmao_huayuan.do_huayuan --need_open_app
```

## 兼容性

### 向后兼容

旧格式仍然支持，会自动使用 `task_operations_tianmao` 模块：

```bash
# 旧格式（仍然支持）
python do_job.py --action do_job

# 等价于新格式
python do_job.py --action task_operations_tianmao.do_job
```

### 参数自动适配

系统会自动检测方法的参数签名，并智能传递参数：

- 如果方法支持 `need_open_app` 和 `is_clone` 参数，会自动传递
- 如果方法只支持 `need_open_app` 参数，只传递该参数
- 如果方法不需要参数，直接调用
- 如果方法有其他未知参数，尝试无参数调用

## 错误处理

系统会提供详细的错误信息：

```bash
# 无效模块
python do_job.py --action invalid_module.method
# 输出: [错误] 无法导入模块 'invalid_module': No module named 'invalid_module'

# 无效方法
python do_job.py --action task_operations_tianmao.invalid_method
# 输出: [错误] 模块 'task_operations_tianmao' 中不存在方法 'invalid_method'
```

## 完整示例

```bash
# 示例1: 使用新格式执行天猫任务
python do_job.py \
  --sb_name "mix2_zi" \
  --sb_url "192.168.123.29:5555" \
  --action "task_operations_tianmao.do_job" \
  --need_open_app \
  --is_clone

# 示例2: 执行花园任务
python do_job.py \
  --sb_name "k80pro" \
  --sb_url "192.168.123.39:36123" \
  --action "task_tianmao_huayuan.auto_drag_matching_elements_loop"

# 示例3: 兼容旧格式
python do_job.py --action "do_test"
```

## 添加新模块和方法

要添加新的模块和方法：

1. 创建新的 Python 模块文件
2. 在模块中定义方法
3. 确保方法参数符合约定（可选的 `need_open_app` 和 `is_clone` 参数）
4. 使用新格式调用：`--action "新模块名.新方法名"`

系统会自动发现和调用新的模块和方法，无需修改 `do_job.py`。
