# task_operations_tianmao.py
import time
import cv2
import numpy as np
import random
from device_utils import get_device, get_log_path,get_device_name
import os
import re

def open_tmall(open_clone=False):
    """
    最终稳定版天猫应用启动（适配您的测试结果）
    参数:
        open_clone (bool): 
            False - 打开原版（左侧图标，默认）
            True - 打开分身（右侧图标）
    返回:
        bool: 是否成功执行
    """
    d = get_device()
    # 获取有效的天猫图标（最多检查2个实例）
    valid_icons = []
    for i in range(2):  # 只检查0和1，因为实例2会报错
        try:
            icon = d(text="天猫", instance=i)
            if icon.exists:
                valid_icons.append({
                    "instance": i,
                    "position": icon.center(),
                    "object": icon
                })
        except:
            continue

    # 根据找到的图标数量处理
    if len(valid_icons) == 0:
        print("[错误] 未找到任何天猫应用")
        return False

    # 按X坐标排序（从左到右）
    sorted_icons = sorted(valid_icons, key=lambda x: x["position"][0])

    # 单图标情况
    if len(sorted_icons) == 1:
        if not open_clone:
            try:
                sorted_icons[0]["object"].click()
                print(f"[成功] 已点击天猫原版（坐标: {sorted_icons[0]['position']}）")
                return True
            except:
                print("[错误] 点击图标失败")
                return False
        print("[错误] 要求打开分身但未找到第二个图标")
        return False

    # 双图标情况
    try:
        target = sorted_icons[1] if open_clone else sorted_icons[0]
        target["object"].click()
        print(
            f"[成功] 已点击天猫{'分身' if open_clone else '原版'}（坐标: {target['position']}）")
        return True
    except:
        print("[错误] 点击图标失败")
        return False
    

def random_swipe(duration_sec=40, interval_sec=2, x_radius=30, 
                y_radius=300, min_swipe_distance=100):
    """
    每隔指定时间在屏幕中心附近随机滑动，持续指定时间

    :param duration_sec: 总持续时间(秒)，默认40秒
    :param interval_sec: 滑动间隔时间(秒)，默认2秒
    :param radius: 中心点附近的像素范围
    :param min_swipe_distance: 最小滑动距离（Y 轴），默认100
    """
    d = get_device()
    # 获取设备屏幕尺寸
    screen_info = d.info
    screen_width = screen_info['displayWidth']
    screen_height = screen_info['displayHeight']
    width, height = screen_width, screen_height
    # 计算中心点坐标
    center_x = width // 2
    center_y = height // 2
    # 计算需要执行的滑动次数
    iterations = int(duration_sec / interval_sec)

    for i in range(iterations):
        # 生成 X 轴随机坐标（限制在中心附近）
        start_x = random.randint(center_x - x_radius, center_x + x_radius)
        end_x = random.randint(center_x - x_radius, center_x + x_radius)

        # 生成 Y 轴随机坐标，并确保滑动距离 ≥ min_swipe_distance
        while True:
            start_y = random.randint(center_y - y_radius, center_y + y_radius)
            end_y = random.randint(center_y - y_radius, center_y + y_radius)
            if abs(start_y - end_y) >= min_swipe_distance:
                break  # 确保滑动距离足够

        # 确保坐标不超出屏幕范围
        start_x = max(0, min(start_x, width - 1))
        start_y = max(0, min(start_y, height - 1))
        end_x = max(0, min(end_x, width - 1))
        end_y = max(0, min(end_y, height - 1))

        # 执行滑动操作
        d.swipe(start_x, start_y, end_x, end_y)

        # 打印当前进度
        print(f"已完成 {i+1}/{iterations} 次滑动，等待下一次...")

        # 如果不是最后一次，则等待间隔时间
        if i < iterations - 1:
            time.sleep(interval_sec)

        # 判断是否完成提前结束
        if find_and_click("返回领奖", click=False, screenshot=False) is not None or find_and_click("任务已完成快去领奖吧", click=False, screenshot=False) is not None:
            print("提示返回了，操作完成")
            break
    print("随机滑动操作完成")



def find_and_click(target_name, match_type='文本', pianyi_x=0, pianyi_y=0,
                   threshold=0.7, click=True, wait_after_click=2, screenshot=True):
    """
    通过指定方式定位元素并点击（可选）

    参数:
        target_name (str): 图片名称/文本内容/XPath表达式
        match_type (str): 匹配方式，必须为'图片'/'文本'/'xpath'
        pianyi_x (int): X轴点击偏移量（仅图片匹配时有效）
        pianyi_y (int): Y轴点击偏移量（仅图片匹配时有效）
        threshold (float): 图片匹配阈值(0-1)
        click (bool): 是否执行点击操作
        wait_after_click (int): 点击后等待时间（秒）
        screenshot (bool): 是否保存截图

    返回:
        bool: 图片匹配时返回是否找到
        object: 文本/xpath匹配时返回元素对象（找不到返回None）
    """

    d = get_device()
    log_path = get_log_path()

    def sanitize_filename(name):
        """清理文件名中的非法字符并限制长度"""
        # 替换非法字符为下划线
        clean_name = re.sub(r'[\\/*?:"<>|]', '_', name)
        # 移除首尾空白字符
        clean_name = clean_name.strip()
        # 限制文件名长度（Windows最大255，保留扩展名空间）
        return clean_name[:200] if len(clean_name) > 200 else clean_name

    if match_type not in ['图片', '文本', 'xpath']:
        raise ValueError("match_type 参数必须为 '图片'/'文本'/'xpath'")

    screenshot_name = f"after_find_and_click_{sanitize_filename(target_name)}.png"

    element = None

    try:
        # 图片匹配分支
        if match_type == '图片':
            target_img_path = target_name + ".png"
            if not os.path.exists(target_img_path):
                print(f"[错误] 图片文件不存在: {target_img_path}")
                return False

            screen_pil = d.screenshot()
            screen_cv = cv2.cvtColor(np.array(screen_pil), cv2.COLOR_RGB2BGR)

            template_img = cv2.imdecode(np.fromfile(
                target_img_path, dtype=np.uint8), cv2.IMREAD_COLOR)
            h, w = template_img.shape[:2]

            res = cv2.matchTemplate(
                screen_cv, template_img, cv2.TM_CCOEFF_NORMED)
            min_val, max_val, min_loc, max_loc = cv2.minMaxLoc(res)

            if max_val >= threshold:
                center_x = max_loc[0] + w // 2
                center_y = max_loc[1] + h // 2
                print(f"✅ 图片匹配成功 [{target_name}] 置信度: {max_val:.2f}")

                if click:
                    click_x = center_x + pianyi_x
                    click_y = center_y + pianyi_y
                    d.click(click_x, click_y)
                    print(f"🖱 已点击坐标: ({click_x}, {click_y})")
                    time.sleep(wait_after_click)

                return True  # 图片匹配始终返回布尔值
            return False

        # 文本匹配分支
        elif match_type == '文本':
            element = d(text=target_name)

        # XPath匹配分支
        elif match_type == 'xpath':
            element = d.xpath(target_name)

        # 处理元素查找结果
        if element.exists:
            print(f"✅ {match_type}匹配成功 [{target_name}]")
            if click:
                element.click()
                print(f"🖱 已点击{match_type}元素")
                time.sleep(wait_after_click)
            return element  # 返回元素对象
        return None  # 找不到返回None

    except Exception as e:
        print(f"❌ 操作异常: {str(e)}")
        return False if match_type == '图片' else None

    finally:
        if screenshot:
            try:
                d.screenshot(os.path.join(log_path, screenshot_name))
                print(f"📸 已保存操作后截图: {screenshot_name}")
            except Exception as e:
                print(f"❌ 截图保存失败: {str(e)}")


def do_huayuan(need_open_app=False, is_clone=False):
    """
        执行自动化任务
        
        Args:
            need_open_app (bool): 是否需要在任务开始前回到桌面并打开应用
                - True: 自动回到桌面并启动应用（适用于应用未打开的情况）
                - False: 假设应用已在任务界面打开（跳过启动步骤）
            is_clone (bool): 标识操作的是否为分身应用
                - True: 操作分身/双开版应用
                - False: 操作原始/官方版应用
        
        Example:
            >>> # 操作原版应用，自动启动场景 "mix2", "xrqlovewsw.vip:8889"
            >>> do_job(need_open_app=True, is_clone=False)
            
            >>> # 操作分身应用，手动已启动场景 "k80pro", "**************:36123"
            >>> do_job(need_open_app=False)
        """
    # 方法实现开始...
    d = get_device()
    log_path = get_log_path()
    sb_name = get_device_name()

    #是否需要打开app
    if need_open_app :
        # 回到主页
        d.press("home")
        # 等待主页加载
        time.sleep(1)
        #打开app 原版或分身
        open_tmall(open_clone=is_clone)

        # 先按几次返回再打开确保在app首页
        for i in range(3):
            d.press("back")
            time.sleep(1)

        # 回到主页
        d.press("home")
        # 等待主页加载
        time.sleep(1)
        #打开app 原版或分身
        open_tmall(open_clone=is_clone)

        time.sleep(2)
        find_and_click("我",wait_after_click=5)
        find_and_click("猫享花园",wait_after_click=5)

    find_and_click("收下")


def detect_dynamic_elements():
    """
    检测屏幕下半部分的动态元素（轻微放大缩小的提示元素）

    返回:
        dict: 检测结果
            - success (bool): 是否成功检测
            - dynamic_elements (list): 动态元素列表，每个元素包含位置和变化信息
            - screenshots (list): 保存的截图路径列表
    """
    d = get_device()
    log_path = get_log_path()

    try:
        print("🔍 开始检测屏幕下半部分的动态元素...")

        # 获取屏幕尺寸
        screen_info = d.info
        screen_width = screen_info['displayWidth']
        screen_height = screen_info['displayHeight']

        # 定义下半部分区域 (从屏幕50%高度开始)
        lower_half_start_y = screen_height // 2
        print(f"📏 屏幕尺寸: {screen_width}x{screen_height}")
        print(f"🎯 检测区域: 下半部分 (y >= {lower_half_start_y})")

        # 连续截取多张图片来检测动态变化
        screenshots = []
        screenshot_paths = []
        capture_count = 6  # 截取6张图片
        interval = 0.5     # 每0.5秒截取一次

        print(f"📸 开始连续截图 ({capture_count}张，间隔{interval}秒)...")

        for i in range(capture_count):
            # 截取屏幕
            screen_pil = d.screenshot()
            screen_cv = cv2.cvtColor(np.array(screen_pil), cv2.COLOR_RGB2BGR)

            # 只保留下半部分
            lower_half = screen_cv[lower_half_start_y:, :]

            screenshots.append(lower_half)

            # 保存截图用于调试
            screenshot_path = os.path.join(log_path, f"dynamic_detection_{i+1}.png")
            cv2.imwrite(screenshot_path, lower_half)
            screenshot_paths.append(screenshot_path)

            print(f"📸 已保存第{i+1}张截图: dynamic_detection_{i+1}.png")

            if i < capture_count - 1:  # 最后一张不需要等待
                time.sleep(interval)

        # 分析动态变化
        dynamic_elements = analyze_dynamic_changes(screenshots, lower_half_start_y)

        # 在最后一张截图上标记检测到的动态元素
        if dynamic_elements:
            marked_screen = screenshots[-1].copy()
            colors = [(0, 255, 0), (255, 0, 0), (0, 0, 255), (255, 255, 0)]

            for i, element in enumerate(dynamic_elements):
                color = colors[i % len(colors)]
                center = element['center']
                # 注意：这里的center是相对于下半部分的坐标
                cv2.circle(marked_screen, center, 30, color, 3)
                cv2.putText(marked_screen, f"Dynamic{i+1}",
                           (center[0]-40, center[1]-40),
                           cv2.FONT_HERSHEY_SIMPLEX, 0.8, color, 2)

                # 绘制变化区域
                if 'bounds' in element:
                    x1, y1, x2, y2 = element['bounds']
                    cv2.rectangle(marked_screen, (x1, y1), (x2, y2), color, 2)

            marked_path = os.path.join(log_path, "dynamic_elements_marked.png")
            cv2.imwrite(marked_path, marked_screen)
            print(f"📸 已保存标记截图: dynamic_elements_marked.png")

        return {
            "success": len(dynamic_elements) > 0,
            "dynamic_elements": dynamic_elements,
            "screenshots": screenshot_paths
        }

    except Exception as e:
        print(f"❌ 检测动态元素时发生异常: {str(e)}")
        return {
            "success": False,
            "dynamic_elements": [],
            "screenshots": []
        }


def analyze_dynamic_changes(screenshots, offset_y):
    """
    分析截图序列中的动态变化

    参数:
        screenshots: 截图列表
        offset_y: Y轴偏移量（用于转换回全屏坐标）

    返回:
        list: 检测到的动态元素列表
    """
    if len(screenshots) < 2:
        return []

    dynamic_elements = []

    try:
        print("🔍 分析图像变化...")

        # 将所有截图转换为灰度图像以便比较
        gray_screenshots = [cv2.cvtColor(img, cv2.COLOR_BGR2GRAY) for img in screenshots]

        # 计算连续帧之间的差异
        diff_maps = []
        for i in range(len(gray_screenshots) - 1):
            diff = cv2.absdiff(gray_screenshots[i], gray_screenshots[i + 1])
            diff_maps.append(diff)

        # 累积所有差异图
        accumulated_diff = np.zeros_like(diff_maps[0], dtype=np.float32)
        for diff in diff_maps:
            accumulated_diff += diff.astype(np.float32)

        # 归一化累积差异
        accumulated_diff = (accumulated_diff / len(diff_maps)).astype(np.uint8)

        # 应用阈值来找到显著变化的区域
        threshold = 15  # 可以根据需要调整
        _, binary_diff = cv2.threshold(accumulated_diff, threshold, 255, cv2.THRESH_BINARY)

        # 形态学操作来清理噪声
        kernel = cv2.getStructuringElement(cv2.MORPH_ELLIPSE, (5, 5))
        binary_diff = cv2.morphologyEx(binary_diff, cv2.MORPH_CLOSE, kernel)
        binary_diff = cv2.morphologyEx(binary_diff, cv2.MORPH_OPEN, kernel)

        # 查找轮廓
        contours, _ = cv2.findContours(binary_diff, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)

        # 过滤轮廓，找到可能的动态元素
        min_area = 500   # 最小面积阈值
        max_area = 50000 # 最大面积阈值

        for i, contour in enumerate(contours):
            area = cv2.contourArea(contour)

            if min_area <= area <= max_area:
                # 计算边界框
                x, y, w, h = cv2.boundingRect(contour)

                # 计算中心点（转换回全屏坐标）
                center_x = x + w // 2
                center_y = y + h // 2
                full_center_y = center_y + offset_y  # 加上偏移量得到全屏坐标

                # 计算变化强度
                roi_diff = accumulated_diff[y:y+h, x:x+w]
                change_intensity = np.mean(roi_diff)

                dynamic_element = {
                    'id': i + 1,
                    'center': (center_x, center_y),  # 相对于下半部分的坐标
                    'full_screen_center': (center_x, full_center_y),  # 全屏坐标
                    'bounds': (x, y, x + w, y + h),  # 相对于下半部分的坐标
                    'area': area,
                    'change_intensity': change_intensity,
                    'full_screen_bounds': (x, y + offset_y, x + w, y + h + offset_y)  # 全屏坐标
                }

                dynamic_elements.append(dynamic_element)

                print(f"🎯 检测到动态元素 {i+1}:")
                print(f"   中心坐标(全屏): ({center_x}, {full_center_y})")
                print(f"   区域大小: {w}x{h} (面积: {area})")
                print(f"   变化强度: {change_intensity:.2f}")

        # 按变化强度排序，最活跃的排在前面
        dynamic_elements.sort(key=lambda x: x['change_intensity'], reverse=True)

        print(f"✅ 总共检测到 {len(dynamic_elements)} 个动态元素")

        return dynamic_elements

    except Exception as e:
        print(f"❌ 分析动态变化时出错: {str(e)}")
        return []


def find_matching_elements_in_lower_half():
    """
    在屏幕下半部分查找相同的元素（使用图像识别）

    返回:
        dict: 检测结果
            - success (bool): 是否成功找到相同元素
            - matching_pairs (list): 相同元素的坐标对列表
            - all_elements (list): 所有检测到的元素信息
    """
    d = get_device()
    log_path = get_log_path()

    try:
        print("🔍 开始在屏幕下半部分查找相同元素...")

        # 截取屏幕
        screen_pil = d.screenshot()
        screen_cv = cv2.cvtColor(np.array(screen_pil), cv2.COLOR_RGB2BGR)
        screen_height, screen_width = screen_cv.shape[:2]

        # 定义下半部分区域 (从屏幕50%高度开始)
        lower_half_start_y = screen_height // 2
        lower_half = screen_cv[lower_half_start_y:, :]

        print(f"📏 屏幕尺寸: {screen_width}x{screen_height}")
        print(f"🎯 分析区域: 下半部分 (y >= {lower_half_start_y})")

        # 保存下半部分截图用于调试
        lower_half_path = os.path.join(log_path, "lower_half_analysis.png")
        cv2.imwrite(lower_half_path, lower_half)
        print(f"📸 已保存下半部分截图: lower_half_analysis.png")

        # 检测下半部分的所有可能元素
        elements = detect_elements_in_region(lower_half, lower_half_start_y)

        if len(elements) < 2:
            print(f"❌ 检测到的元素数量不足 (找到 {len(elements)}，需要至少2个)")
            return {"success": False, "matching_pairs": [], "all_elements": elements}

        # 查找相同的元素
        matching_pairs = find_similar_elements(elements)

        if not matching_pairs:
            print("❌ 未找到相同的元素")
            return {"success": False, "matching_pairs": [], "all_elements": elements}

        # 在截图上标记找到的相同元素
        marked_screen = lower_half.copy()
        colors = [(0, 255, 0), (255, 0, 0), (0, 0, 255), (255, 255, 0), (255, 0, 255)]

        for i, pair in enumerate(matching_pairs):
            color = colors[i % len(colors)]
            for j, element in enumerate(pair):
                center = element['center_relative']  # 相对于下半部分的坐标
                cv2.circle(marked_screen, center, 25, color, 3)
                cv2.putText(marked_screen, f"Match{i+1}-{j+1}",
                           (center[0]-30, center[1]-35),
                           cv2.FONT_HERSHEY_SIMPLEX, 0.6, color, 2)

        marked_path = os.path.join(log_path, "matching_elements_marked.png")
        cv2.imwrite(marked_path, marked_screen)
        print(f"📸 已保存匹配标记截图: matching_elements_marked.png")

        print(f"✅ 找到 {len(matching_pairs)} 组相同元素")
        for i, pair in enumerate(matching_pairs):
            print(f"   第{i+1}组: {len(pair)}个相同元素")

        return {
            "success": True,
            "matching_pairs": matching_pairs,
            "all_elements": elements
        }

    except Exception as e:
        print(f"❌ 查找相同元素时发生异常: {str(e)}")
        return {"success": False, "matching_pairs": [], "all_elements": []}


def detect_elements_in_region(region_img, offset_y):
    """
    在指定区域检测所有可能的元素

    参数:
        region_img: 区域图像
        offset_y: Y轴偏移量（用于转换回全屏坐标）

    返回:
        list: 检测到的元素列表
    """
    elements = []

    try:
        # 转换为灰度图像进行轮廓检测
        gray = cv2.cvtColor(region_img, cv2.COLOR_BGR2GRAY)

        # 使用自适应阈值
        binary = cv2.adaptiveThreshold(gray, 255, cv2.ADAPTIVE_THRESH_GAUSSIAN_C,
                                      cv2.THRESH_BINARY, 11, 2)

        # 形态学操作来清理图像
        kernel = cv2.getStructuringElement(cv2.MORPH_ELLIPSE, (3, 3))
        binary = cv2.morphologyEx(binary, cv2.MORPH_CLOSE, kernel)
        binary = cv2.morphologyEx(binary, cv2.MORPH_OPEN, kernel)

        # 查找轮廓
        contours, _ = cv2.findContours(binary, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)

        # 过滤轮廓，找到可能的元素 - 放宽条件
        min_area = 400   # 降低最小面积阈值
        max_area = 25000 # 提高最大面积阈值

        for i, contour in enumerate(contours):
            area = cv2.contourArea(contour)

            if min_area <= area <= max_area:
                # 计算边界框
                x, y, w, h = cv2.boundingRect(contour)

                # 放宽形状过滤条件
                aspect_ratio = w / h
                if 0.2 <= aspect_ratio <= 5.0:  # 放宽宽高比范围

                    # 提取元素图像
                    element_img = region_img[y:y+h, x:x+w]

                    # 计算中心点
                    center_x = x + w // 2
                    center_y = y + h // 2
                    full_center_y = center_y + offset_y

                    element = {
                        'id': i,
                        'center_relative': (center_x, center_y),  # 相对于区域的坐标
                        'center_full': (center_x, full_center_y),  # 全屏坐标
                        'bounds_relative': (x, y, x + w, y + h),  # 相对于区域的边界
                        'bounds_full': (x, y + offset_y, x + w, y + h + offset_y),  # 全屏边界
                        'area': area,
                        'width': w,
                        'height': h,
                        'aspect_ratio': aspect_ratio,
                        'image': element_img
                    }

                    elements.append(element)

        print(f"🔍 在区域中检测到 {len(elements)} 个可能的元素")

        return elements

    except Exception as e:
        print(f"❌ 检测区域元素时出错: {str(e)}")
        return []


def find_similar_elements(elements, similarity_threshold=0.55):
    """
    在元素列表中查找相似的元素 - 优化版本

    参数:
        elements: 元素列表
        similarity_threshold: 相似度阈值 (降低到0.55)

    返回:
        list: 相似元素组的列表
    """
    print(f"🔍 开始分析 {len(elements)} 个元素的相似度...")

    # 首先显示所有元素的基本信息
    for i, element in enumerate(elements):
        center = element['center_full']
        area = element['area']
        size = f"{element['width']}x{element['height']}"
        print(f"   元素{i}: 中心({center[0]}, {center[1]}) 面积:{area} 尺寸:{size}")

    matching_pairs = []
    processed_elements = set()

    # 计算所有元素对的相似度矩阵
    similarity_matrix = {}
    for i, element1 in enumerate(elements):
        for j, element2 in enumerate(elements[i+1:], i+1):
            similarity = calculate_element_similarity(element1, element2)
            similarity_matrix[(i, j)] = similarity
            print(f"🔍 元素{i} vs 元素{j}: 相似度 {similarity:.3f}")

    # 使用更宽松的阈值查找相似元素
    for i, element1 in enumerate(elements):
        if i in processed_elements:
            continue

        current_group = [element1]
        processed_elements.add(i)

        for j, element2 in enumerate(elements[i+1:], i+1):
            if j in processed_elements:
                continue

            similarity = similarity_matrix.get((i, j), 0)

            if similarity >= similarity_threshold:
                current_group.append(element2)
                processed_elements.add(j)
                print(f"🌟 找到相似元素: 元素{i} 和 元素{j} (相似度: {similarity:.3f})")

        # 只有当找到2个或更多相似元素时才添加到结果中
        if len(current_group) >= 2:
            matching_pairs.append(current_group)
            print(f"✅ 发现 {len(current_group)} 个相似元素组")

    # 如果没有找到匹配，尝试更低的阈值
    if not matching_pairs and similarity_matrix:
        print("⚠️ 使用标准阈值未找到匹配，尝试更低阈值...")
        lower_threshold = 0.35
        processed_elements = set()

        for i, element1 in enumerate(elements):
            if i in processed_elements:
                continue

            current_group = [element1]
            processed_elements.add(i)

            for j, element2 in enumerate(elements[i+1:], i+1):
                if j in processed_elements:
                    continue

                similarity = similarity_matrix.get((i, j), 0)

                if similarity >= lower_threshold:
                    current_group.append(element2)
                    processed_elements.add(j)
                    print(f"🌟 (低阈值)找到相似元素: 元素{i} 和 元素{j} (相似度: {similarity:.3f})")

            if len(current_group) >= 2:
                matching_pairs.append(current_group)
                print(f"✅ (低阈值)发现 {len(current_group)} 个相似元素组")

    return matching_pairs


def calculate_element_similarity(element1, element2):
    """
    计算两个元素的相似度 - 优化版本

    参数:
        element1, element2: 要比较的元素

    返回:
        float: 相似度值 (0-1)
    """
    try:
        img1 = element1['image']
        img2 = element2['image']

        # 首先检查尺寸相似性
        size_similarity = calculate_size_similarity(element1, element2)

        # 如果尺寸差异太大，直接返回低相似度
        if size_similarity < 0.3:
            return 0.0

        # 调整图像大小使其一致 - 使用固定尺寸以提高比较稳定性
        target_size = 64  # 使用固定的64x64尺寸
        img1_resized = cv2.resize(img1, (target_size, target_size))
        img2_resized = cv2.resize(img2, (target_size, target_size))

        # 方法1: 模板匹配 (权重增加)
        res = cv2.matchTemplate(img1_resized, img2_resized, cv2.TM_CCOEFF_NORMED)
        _, template_similarity, _, _ = cv2.minMaxLoc(res)
        template_similarity = max(0.0, template_similarity)

        # 方法2: 直方图比较 - 使用多种比较方法
        hist1 = cv2.calcHist([img1_resized], [0, 1, 2], None, [32, 32, 32], [0, 256, 0, 256, 0, 256])
        hist2 = cv2.calcHist([img2_resized], [0, 1, 2], None, [32, 32, 32], [0, 256, 0, 256, 0, 256])

        # 使用相关性比较
        hist_correl = cv2.compareHist(hist1, hist2, cv2.HISTCMP_CORREL)
        hist_correl = max(0.0, hist_correl)

        # 使用卡方比较 (转换为相似度)
        hist_chisqr = cv2.compareHist(hist1, hist2, cv2.HISTCMP_CHISQR)
        hist_chisqr_sim = 1.0 / (1.0 + hist_chisqr)

        # 综合直方图相似度
        hist_similarity = (hist_correl * 0.7 + hist_chisqr_sim * 0.3)

        # 方法3: 边缘特征比较
        edge_similarity = calculate_edge_similarity(img1_resized, img2_resized)

        # 方法4: 颜色特征比较
        color_similarity = calculate_color_similarity(img1_resized, img2_resized)

        # 综合所有方法的结果 - 调整权重
        combined_similarity = (
            template_similarity * 0.35 +    # 模板匹配权重
            hist_similarity * 0.25 +        # 直方图权重
            edge_similarity * 0.20 +        # 边缘特征权重
            color_similarity * 0.15 +       # 颜色特征权重
            size_similarity * 0.05           # 尺寸相似性权重
        )

        return max(0.0, min(1.0, combined_similarity))

    except Exception as e:
        print(f"❌ 计算元素相似度时出错: {str(e)}")
        return 0.0


def calculate_size_similarity(element1, element2):
    """计算两个元素的尺寸相似度"""
    try:
        area1 = element1['area']
        area2 = element2['area']

        w1, h1 = element1['width'], element1['height']
        w2, h2 = element2['width'], element2['height']

        # 面积相似度
        area_ratio = min(area1, area2) / max(area1, area2)

        # 宽高比相似度
        aspect1 = w1 / h1
        aspect2 = w2 / h2
        aspect_ratio = min(aspect1, aspect2) / max(aspect1, aspect2)

        # 综合尺寸相似度
        size_similarity = (area_ratio * 0.6 + aspect_ratio * 0.4)

        return size_similarity

    except:
        return 0.5


def calculate_edge_similarity(img1, img2):
    """计算边缘特征相似度"""
    try:
        # 转换为灰度图
        gray1 = cv2.cvtColor(img1, cv2.COLOR_BGR2GRAY)
        gray2 = cv2.cvtColor(img2, cv2.COLOR_BGR2GRAY)

        # 计算边缘
        edges1 = cv2.Canny(gray1, 50, 150)
        edges2 = cv2.Canny(gray2, 50, 150)

        # 计算边缘像素比例
        edge_ratio1 = np.sum(edges1 > 0) / (edges1.shape[0] * edges1.shape[1])
        edge_ratio2 = np.sum(edges2 > 0) / (edges2.shape[0] * edges2.shape[1])

        # 边缘比例相似度
        ratio_similarity = 1.0 - abs(edge_ratio1 - edge_ratio2)

        # 边缘模式相似度
        if edge_ratio1 > 0 and edge_ratio2 > 0:
            # 使用模板匹配比较边缘模式
            res = cv2.matchTemplate(edges1, edges2, cv2.TM_CCOEFF_NORMED)
            _, pattern_similarity, _, _ = cv2.minMaxLoc(res)
            pattern_similarity = max(0.0, pattern_similarity)
        else:
            pattern_similarity = 0.5

        return (ratio_similarity * 0.4 + pattern_similarity * 0.6)

    except:
        return 0.5


def calculate_color_similarity(img1, img2):
    """计算颜色特征相似度"""
    try:
        # 计算平均颜色
        mean1 = np.mean(img1, axis=(0, 1))
        mean2 = np.mean(img2, axis=(0, 1))

        # 颜色距离
        color_distance = np.linalg.norm(mean1 - mean2)
        color_similarity = 1.0 / (1.0 + color_distance / 100.0)

        # 计算颜色分布的标准差
        std1 = np.std(img1, axis=(0, 1))
        std2 = np.std(img2, axis=(0, 1))

        std_distance = np.linalg.norm(std1 - std2)
        std_similarity = 1.0 / (1.0 + std_distance / 50.0)

        return (color_similarity * 0.6 + std_similarity * 0.4)

    except:
        return 0.5


def drag_matching_elements():
    """
    查找屏幕下半部分的相同元素，并将第一个元素拖动到第二个元素上

    返回:
        bool: 是否成功拖动了相同元素
    """
    result = find_matching_elements_in_lower_half()

    if not result["success"]:
        print("❌ 未找到相同元素")
        return False

    matching_pairs = result["matching_pairs"]

    if not matching_pairs:
        print("❌ 没有找到匹配的元素对")
        return False

    # 获取第一组匹配的元素
    first_pair = matching_pairs[0]

    if len(first_pair) < 2:
        print("❌ 第一组匹配元素数量不足")
        return False

    # 获取前两个元素进行拖动
    source_element = first_pair[0]
    target_element = first_pair[1]

    source_x, source_y = source_element['center_full']
    target_x, target_y = target_element['center_full']

    print(f"🔍 找到相同元素:")
    print(f"   源元素: ({source_x}, {source_y})")
    print(f"   目标元素: ({target_x}, {target_y})")

    d = get_device()

    try:
        # 执行拖动操作
        print(f"🖱 开始拖动: ({source_x}, {source_y}) → ({target_x}, {target_y})")

        d.swipe(source_x, source_y, target_x, target_y, duration=1.0)

        print("✅ 拖动操作完成")
        time.sleep(2)  # 拖动后等待2秒

        return True

    except Exception as e:
        print(f"❌ 拖动相同元素时发生异常: {str(e)}")
        return False


def auto_drag_matching_elements_loop():
    """
    循环执行相同元素拖动操作，如果失败则尝试点击"召唤"按钮

    如果连续3次点击召唤按钮后仍然失败，则停止循环

    返回:
        bool: 是否至少成功执行了一次拖动操作
    """
    max_summon_failures = 3  # 最大连续召唤失败次数
    summon_failure_count = 0  # 当前连续召唤失败计数
    success_count = 0  # 成功拖动次数

    print("🔄 开始自动拖动相同元素循环...")

    while summon_failure_count < max_summon_failures:
        # 尝试拖动相同元素
        drag_success = drag_matching_elements()

        if drag_success:
            # 拖动成功，重置失败计数
            print("✅ 成功完成一次拖动操作")
            summon_failure_count = 0
            success_count += 1
            time.sleep(2)  # 成功后等待2秒
            continue

        # 拖动失败，尝试点击"召唤"按钮
        print("⚠️ 拖动失败，尝试点击'召唤'按钮...")

        # 尝试查找并点击"召唤"按钮
        summon_img_path = os.path.join("img_huayuan", "召唤.png")
        if not os.path.exists(summon_img_path):
            print(f"❌ 召唤按钮图片不存在: {summon_img_path}")
            print("💡 请确保在 img_huayuan 文件夹下有 '召唤.png' 图片文件")
            return success_count > 0

        # 使用find_and_click函数点击召唤按钮
        summon_clicked = find_and_click(os.path.join("img_huayuan", "召唤"), match_type='图片', wait_after_click=0)

        if not summon_clicked:
            print("❌ 未找到或点击'召唤'按钮失败")
            summon_failure_count += 1
            print(f"⚠️ 召唤失败计数: {summon_failure_count}/{max_summon_failures}")
        else:
            print("✅ 已点击'召唤'按钮，等待5秒...")
            time.sleep(5)  # 点击召唤后等待5秒

            # 再次尝试拖动
            drag_success = drag_matching_elements()

            if not drag_success:
                # 召唤后拖动仍然失败
                summon_failure_count += 1
                print(f"⚠️ 召唤后拖动仍然失败，召唤失败计数: {summon_failure_count}/{max_summon_failures}")
            else:
                # 召唤后拖动成功
                print("✅ 召唤后拖动成功")
                summon_failure_count = 0
                success_count += 1

        # 每次循环间隔
        time.sleep(2)

    print(f"🛑 循环结束: 连续{max_summon_failures}次召唤后拖动失败")
    print(f"📊 总计成功拖动次数: {success_count}")

    return success_count > 0


def drag_dynamic_elements():
    """
    检测屏幕下半部分的动态元素，并将第一个元素拖动到第二个元素上

    返回:
        bool: 是否成功拖动了动态元素
    """
    result = detect_dynamic_elements()

    if not result["success"]:
        print("❌ 未检测到动态元素")
        return False

    dynamic_elements = result["dynamic_elements"]

    # 需要至少两个动态元素
    if len(dynamic_elements) < 2:
        print(f"❌ 检测到的动态元素数量不足 (找到 {len(dynamic_elements)}，需要至少2个)")
        return False

    # 获取前两个最活跃的动态元素
    source_element = dynamic_elements[0]  # 拖动源
    target_element = dynamic_elements[1]  # 拖动目标

    source_x, source_y = source_element['full_screen_center']
    target_x, target_y = target_element['full_screen_center']

    print(f"🔍 找到两个动态元素:")
    print(f"   源元素: ({source_x}, {source_y})")
    print(f"   目标元素: ({target_x}, {target_y})")

    d = get_device()

    try:
        # 执行拖动操作
        print(f"🖱 开始拖动: ({source_x}, {source_y}) → ({target_x}, {target_y})")

        # 拖动操作 - 先长按源元素，然后移动到目标元素，最后释放
        d.swipe(source_x, source_y, target_x, target_y, duration=1.0)  # 1秒的拖动时间

        print("✅ 拖动操作完成")
        time.sleep(2)  # 拖动后等待2秒

        return True

    except Exception as e:
        print(f"❌ 拖动动态元素时发生异常: {str(e)}")
        return False


def click_dynamic_elements():
    """
    检测并点击屏幕下半部分的动态元素

    返回:
        bool: 是否成功点击了动态元素
    """
    result = detect_dynamic_elements()

    if not result["success"]:
        print("❌ 未检测到动态元素")
        return False

    dynamic_elements = result["dynamic_elements"]

    # 最多点击前两个最活跃的动态元素
    elements_to_click = dynamic_elements[:min(2, len(dynamic_elements))]

    d = get_device()

    try:
        for i, element in enumerate(elements_to_click):
            center_x, center_y = element['full_screen_center']
            print(f"🖱 点击第 {i+1} 个动态元素，坐标: ({center_x}, {center_y})")
            d.click(center_x, center_y)
            time.sleep(1.5)  # 点击后等待1.5秒

        print(f"✅ 成功点击了 {len(elements_to_click)} 个动态元素")
        return True

    except Exception as e:
        print(f"❌ 点击动态元素时发生异常: {str(e)}")
        return False


# ==================== 中文方法名 ====================
# 以下是中文方法名，直接调用对应的英文方法

def 花园任务(need_open_app=False, is_clone=False):
    """执行花园任务"""
    return do_huayuan(need_open_app, is_clone)

def 自动拖拽():
    """执行自动拖拽任务"""
    return auto_drag_matching_elements_loop()
