import uiautomator2 as u2
import time
import os
import cv2
import numpy as np
import random
import re


def ensure_directory_exists(project_path, folder_name):
    """
    确保项目路径下存在指定的文件夹，如果不存在则创建

    参数:
        project_path (str): 项目根目录路径
        folder_name (str): 要检查/创建的文件夹名称

    返回:
        str: 最终确定的文件夹完整路径
    """
    folder_path = os.path.join(project_path, folder_name)

    if not os.path.exists(folder_path):
        try:
            os.makedirs(folder_path)
            print(f"文件夹 '{folder_name}' 已创建于: {folder_path}")
        except OSError as e:
            print(f"创建文件夹 '{folder_name}' 失败: {e}")
    else:
        print(f"文件夹 '{folder_name}' 已存在: {folder_path}")

    return folder_path


def open_tmall(open_clone=False):
    """
    最终稳定版天猫应用启动（适配您的测试结果）
    参数:
        open_clone (bool): 
            False - 打开原版（左侧图标，默认）
            True - 打开分身（右侧图标）
    返回:
        bool: 是否成功执行
    """
    # 获取有效的天猫图标（最多检查2个实例）
    valid_icons = []
    for i in range(2):  # 只检查0和1，因为实例2会报错
        try:
            icon = d(text="天猫", instance=i)
            if icon.exists:
                valid_icons.append({
                    "instance": i,
                    "position": icon.center(),
                    "object": icon
                })
        except:
            continue

    # 根据找到的图标数量处理
    if len(valid_icons) == 0:
        print("❌ 错误：未找到任何天猫应用")
        return False

    # 按X坐标排序（从左到右）
    sorted_icons = sorted(valid_icons, key=lambda x: x["position"][0])

    # 单图标情况
    if len(sorted_icons) == 1:
        if not open_clone:
            try:
                sorted_icons[0]["object"].click()
                print(f"✅ 已点击天猫原版（坐标: {sorted_icons[0]['position']}）")
                return True
            except:
                print("❌ 错误：点击图标失败")
                return False
        print("❌ 错误：要求打开分身但未找到第二个图标")
        return False

    # 双图标情况
    try:
        target = sorted_icons[1] if open_clone else sorted_icons[0]
        target["object"].click()
        print(
            f"✅ 已点击天猫{'分身' if open_clone else '原版'}（坐标: {target['position']}）")
        return True
    except:
        print("❌ 错误：点击图标失败")
        return False


def random_swipe(duration_sec=40, interval_sec=2, x_radius=30, y_radius=300, min_swipe_distance=100):
    """
    每隔指定时间在屏幕中心附近随机滑动，持续指定时间

    :param duration_sec: 总持续时间(秒)，默认40秒
    :param interval_sec: 滑动间隔时间(秒)，默认2秒
    :param radius: 中心点附近的像素范围
    :param min_swipe_distance: 最小滑动距离（Y 轴），默认100
    """

    # 获取设备屏幕尺寸
    width, height = screen_width, screen_height
    # 计算中心点坐标
    center_x = width // 2
    center_y = height // 2
    # 计算需要执行的滑动次数
    iterations = int(duration_sec / interval_sec)

    for i in range(iterations):
        # 生成 X 轴随机坐标（限制在中心附近）
        start_x = random.randint(center_x - x_radius, center_x + x_radius)
        end_x = random.randint(center_x - x_radius, center_x + x_radius)

        # 生成 Y 轴随机坐标，并确保滑动距离 ≥ min_swipe_distance
        while True:
            start_y = random.randint(center_y - y_radius, center_y + y_radius)
            end_y = random.randint(center_y - y_radius, center_y + y_radius)
            if abs(start_y - end_y) >= min_swipe_distance:
                break  # 确保滑动距离足够

        # 确保坐标不超出屏幕范围
        start_x = max(0, min(start_x, width - 1))
        start_y = max(0, min(start_y, height - 1))
        end_x = max(0, min(end_x, width - 1))
        end_y = max(0, min(end_y, height - 1))

        # 执行滑动操作
        d.swipe(start_x, start_y, end_x, end_y)

        # 打印当前进度
        print(f"已完成 {i+1}/{iterations} 次滑动，等待下一次...")

        # 如果不是最后一次，则等待间隔时间
        if i < iterations - 1:
            time.sleep(interval_sec)

        # 判断是否完成提前结束
        if find_and_click("返回领奖", click=False, screenshot=False) or find_and_click("任务已完成快去领奖吧", click=False, screenshot=False):
            print("提示返回了，操作完成")
            break
    print("随机滑动操作完成")


def find_and_click(target_name, match_type='文本', pianyi_x=0, pianyi_y=0,
                   threshold=0.7, click=True, wait_after_click=2, screenshot=True):
    """
    通过指定方式定位元素并点击（可选）

    参数:
        target_name (str): 图片名称/文本内容/XPath表达式
        match_type (str): 匹配方式，必须为'图片'/'文本'/'xpath'
        pianyi_x (int): X轴点击偏移量（仅图片匹配时有效）
        pianyi_y (int): Y轴点击偏移量（仅图片匹配时有效）
        threshold (float): 图片匹配阈值(0-1)
        click (bool): 是否执行点击操作
        wait_after_click (int): 点击后等待时间（秒）
        screenshot (bool): 是否保存截图

    返回:
        bool: 图片匹配时返回是否找到
        object: 文本/xpath匹配时返回元素对象（找不到返回None）
    """
    def sanitize_filename(name):
        """清理文件名中的非法字符并限制长度"""
        # 替换非法字符为下划线
        clean_name = re.sub(r'[\\/*?:"<>|]', '_', name)
        # 移除首尾空白字符
        clean_name = clean_name.strip()
        # 限制文件名长度（Windows最大255，保留扩展名空间）
        return clean_name[:200] if len(clean_name) > 200 else clean_name

    if match_type not in ['图片', '文本', 'xpath']:
        raise ValueError("match_type 参数必须为 '图片'/'文本'/'xpath'")

    screenshot_name = f"after_find_and_click_{sanitize_filename(target_name)}.png"

    element = None

    try:
        # 图片匹配分支
        if match_type == '图片':
            target_img_path = target_name + ".png"
            if not os.path.exists(target_img_path):
                print(f"❌ 图片文件不存在: {target_img_path}")
                return False

            screen_pil = d.screenshot()
            screen_cv = cv2.cvtColor(np.array(screen_pil), cv2.COLOR_RGB2BGR)

            template_img = cv2.imdecode(np.fromfile(
                target_img_path, dtype=np.uint8), cv2.IMREAD_COLOR)
            h, w = template_img.shape[:2]

            res = cv2.matchTemplate(
                screen_cv, template_img, cv2.TM_CCOEFF_NORMED)
            min_val, max_val, min_loc, max_loc = cv2.minMaxLoc(res)

            if max_val >= threshold:
                center_x = max_loc[0] + w // 2
                center_y = max_loc[1] + h // 2
                print(f"✅ 图片匹配成功 [{target_name}] 置信度: {max_val:.2f}")

                if click:
                    click_x = center_x + pianyi_x
                    click_y = center_y + pianyi_y
                    d.click(click_x, click_y)
                    print(f"🖱 已点击坐标: ({click_x}, {click_y})")
                    time.sleep(wait_after_click)

                return True  # 图片匹配始终返回布尔值
            return False

        # 文本匹配分支
        elif match_type == '文本':
            element = d(text=target_name)

        # XPath匹配分支
        elif match_type == 'xpath':
            element = d.xpath(target_name)

        # 处理元素查找结果
        if element.exists:
            print(f"✅ {match_type}匹配成功 [{target_name}]")
            if click:
                element.click()
                print(f"🖱 已点击{match_type}元素")
                time.sleep(wait_after_click)
            return element  # 返回元素对象
        return None  # 找不到返回None

    except Exception as e:
        print(f"❌ 操作异常: {str(e)}")
        return False if match_type == '图片' else None

    finally:
        if screenshot:
            try:
                d.screenshot(os.path.join(log_path, screenshot_name))
                print(f"📸 已保存操作后截图: {screenshot_name}")
            except Exception as e:
                print(f"❌ 截图保存失败: {str(e)}")


def get_log_path(sb_name, sb_url):
    project_path = os.getcwd()  # 默认使用当前工作目录，你可以替换为你的项目路径
    translation_table = str.maketrans({'.': '_', ':': '_'})
    formatted = sb_url.translate(translation_table)
    # 创建img_log_screenshot目录结构
    base_log_dir = os.path.join(project_path, "img_log_screenshot")
    log_path = ensure_directory_exists(base_log_dir, sb_name+'_'+formatted)
    print("log路径:", log_path)
    return log_path


sb_name = "mix2"
sb_url = 'xrqlovewsw.vip:8889'

# sb_name = 'mix2_lan'
# sb_url = '***************:5555'

# sb_name = 'mix2_hong'
# sb_url = '***************:5555'

# sb_name = 'mix2_zi'
# sb_url = '**************:5555'

# sb_name = 'k80pro'
# sb_url = '**************:36123'

# sb_name = 'zte'
# sb_url = '*************:5555'

log_path = get_log_path(sb_name, sb_url)


d = u2.connect(sb_url)
print(d.info)     # 打印设备信息（如屏幕分辨率、系统版本）
# 获取屏幕尺寸
screen_width = d.info['displayWidth']
screen_height = d.info['displayHeight']
print(f"屏幕尺寸: {screen_width}x{screen_height}")

def do_job(is_clone=True):
    # 回到主页
    d.press("home")
    # 等待主页加载
    time.sleep(1)


    open_tmall(open_clone=is_clone)

    find_and_click("我")
    find_and_click("福气红包",wait_after_click=5)



    find_and_click("兑红包红包天天上新",wait_after_click=5)

    find_and_click('//*[@text="通用红包"]/following-sibling::*[@text="2000可兑"]/following-sibling::*[@text="点击兑换"]',match_type='xpath')

    find_and_click('(//*[@text="确认兑换"])[2]',match_type='xpath')

    if find_and_click('//*[contains(@text,"今日已兑换")]',match_type='xpath'):
        print('今日已兑换')
        return True
    else:
        print('今日未兑换,继续尝试操作')
        return False
    

while True:

    if do_job(False) :
        break
    else:
        d.press('back')
        time.sleep(1)
        d.press('back')
        time.sleep(1)
        d.press('back')
        time.sleep(1)
        continue

    
while True:

    if do_job(True) :
        break
    else:
        d.press('back')
        time.sleep(1)
        d.press('back')
        time.sleep(1)
        d.press('back')
        time.sleep(1)
        continue    
    

    