from device_utils import connect_device, get_device
import task_operations_tianmao
import task_tianmao_huayuan
import 天猫
import argparse
import importlib
import inspect
import time

def open_app(app_name, is_clone=False):
    """
    通用的打开应用方法

    Args:
        app_name (str): 应用名称（如"天猫"）
        is_clone (bool): 是否打开分身版本

    Returns:
        bool: 是否成功打开应用
    """
    d = get_device()

    try:
        # 回到主页
        d.press("home")
        time.sleep(1)

        # 获取有效的应用图标（最多检查2个实例）
        valid_icons = []
        for i in range(2):  # 只检查0和1，因为实例2会报错
            try:
                icon = d(text=app_name, instance=i)
                if icon.exists:
                    valid_icons.append({
                        "instance": i,
                        "position": icon.center(),
                        "object": icon
                    })
            except:
                continue

        # 根据找到的图标数量处理
        if len(valid_icons) == 0:
            print(f"[错误] 未找到任何{app_name}应用")
            return False

        # 按X坐标排序（从左到右）
        sorted_icons = sorted(valid_icons, key=lambda x: x["position"][0])

        # 单图标情况
        if len(sorted_icons) == 1:
            if not is_clone:
                try:
                    sorted_icons[0]["object"].click()
                    print(f"[成功] 已点击{app_name}原版（坐标: {sorted_icons[0]['position']}）")
                    time.sleep(2)  # 等待应用启动
                    return True
                except:
                    print("[错误] 点击图标失败")
                    return False
            print(f"[错误] 要求打开{app_name}分身但未找到第二个图标")
            return False

        # 双图标情况
        try:
            target = sorted_icons[1] if is_clone else sorted_icons[0]
            target["object"].click()
            print(f"[成功] 已点击{app_name}{'分身' if is_clone else '原版'}（坐标: {target['position']}）")
            time.sleep(2)  # 等待应用启动
            return True
        except:
            print("[错误] 点击图标失败")
            return False

    except Exception as e:
        print(f"[错误] 打开{app_name}应用时发生异常: {str(e)}")
        return False

def execute_action(action_str):
    """
    根据 action_str 动态执行指定模块的方法

    Args:
        action_str (str): 格式为 "应用名.方法名"，如 "天猫.福气任务"
    """
    try:
        # 应用名到模块名的映射
        app_to_module = {
            '天猫': '天猫',
            '花园': 'task_tianmao_huayuan',
            # 兼容旧格式
            'task_operations_tianmao': 'task_operations_tianmao',
            'task_tianmao_huayuan': 'task_tianmao_huayuan'
        }

        # 方法名映射（中文名到实际方法名）
        method_mapping = {
            '福气任务': 'do_job',
            '测试任务': 'do_test',
            '快手任务': 'do_ks',
            '花园任务': 'do_huayuan',
            '自动拖拽': 'auto_drag_matching_elements_loop',
            # 兼容旧格式
            'do_job': 'do_job',
            'do_test': 'do_test',
            'do_ks': 'do_ks',
            'do_huayuan': 'do_huayuan',
            'auto_drag_matching_elements_loop': 'auto_drag_matching_elements_loop'
        }

        # 解析应用名和方法名
        if '.' not in action_str:
            # 兼容旧格式，默认使用天猫应用
            app_name = '天猫'
            method_display_name = action_str
            print(f"[兼容模式] 使用默认应用: {app_name}.{method_display_name}")
        else:
            app_name, method_display_name = action_str.rsplit('.', 1)

        # 获取实际的模块名和方法名
        module_name = app_to_module.get(app_name)
        if not module_name:
            print(f"[错误] 未知的应用名称: '{app_name}'")
            print(f"[提示] 支持的应用名称: {list(app_to_module.keys())}")
            return False

        actual_method_name = method_mapping.get(method_display_name)
        if not actual_method_name:
            print(f"[错误] 未知的方法名称: '{method_display_name}'")
            print(f"[提示] 支持的方法名称: {list(method_mapping.keys())}")
            return False

        print(f"[执行] 应用: {app_name}, 方法: {method_display_name}")
        print(f"[映射] 模块: {module_name}, 实际方法: {actual_method_name}")

        # 动态导入模块
        try:
            module = importlib.import_module(module_name)
        except ImportError as e:
            print(f"[错误] 无法导入模块 '{module_name}': {str(e)}")
            return False

        # 检查方法是否存在
        if not hasattr(module, actual_method_name):
            print(f"[错误] 模块 '{module_name}' 中不存在方法 '{actual_method_name}'")
            return False

        # 获取方法对象
        method = getattr(module, actual_method_name)

        # 检查是否为可调用对象
        if not callable(method):
            print(f"[错误] '{module_name}.{actual_method_name}' 不是可调用的方法")
            return False

        # 获取方法签名，判断是否需要传递参数
        sig = inspect.signature(method)
        params = list(sig.parameters.keys())

        print(f"[信息] 方法参数: {params}")

        # 简化调用逻辑：直接无参数调用，让方法使用默认参数
        print(f"[调用] {module_name}.{actual_method_name}()")
        method()

        print(f"[成功] 方法 '{app_name}.{method_display_name}' 执行完成")
        return True

    except Exception as e:
        print(f"[错误] 执行方法时发生异常: {str(e)}")
        return False

def main():
    # 设置命令行参数解析
    parser = argparse.ArgumentParser(description='执行设备任务')
    
    # 添加参数
    parser.add_argument('--sb_name', type=str, default='mix2_zi', help='设备名称')
    parser.add_argument('--sb_url', type=str, default='**************:5555', help='设备URL')
    parser.add_argument('--sb_url_params', type=str, default=None, help='设备URL参数')
    parser.add_argument('--need_open_app', action='store_true', help='是否需要在任务开始前回到桌面并打开应用')
    parser.add_argument('--is_clone', action='store_true', help='是否为克隆实例')
    parser.add_argument('--action', type=str, default='天猫.福气任务', help='要执行的方法，格式：应用名.方法名（如 天猫.福气任务）')
    
    # 解析参数
    args = parser.parse_args()
    
    # 设备连接配置
    device_config = {
        "sb_name": args.sb_name,
        "sb_url": args.sb_url,
        "sb_url_params": args.sb_url_params
    }

    # 初始化连接
    try:
        connect_device(**device_config)

        # 如果需要打开应用，先解析应用名并打开
        if args.need_open_app:
            if '.' in args.action:
                app_name = args.action.split('.')[0]
                # 应用名映射
                app_name_mapping = {
                    '天猫': '天猫',
                    '花园': '天猫',  # 花园任务也是在天猫应用中
                    # 兼容旧格式
                    'task_operations_tianmao': '天猫',
                    'task_tianmao_huayuan': '天猫'
                }
                actual_app_name = app_name_mapping.get(app_name, app_name)
                print(f"[准备] 打开应用: {actual_app_name}")

                if not open_app(actual_app_name, args.is_clone):
                    print(f"[错误] 无法打开应用 {actual_app_name}")
                    return
            else:
                # 兼容旧格式，默认打开天猫
                print(f"[准备] 打开默认应用: 天猫")
                if not open_app('天猫', args.is_clone):
                    print(f"[错误] 无法打开应用 天猫")
                    return

        # 根据 action 参数动态执行方法
        execute_action(args.action)
            
    except Exception as e:
        print(f"任务执行失败: {str(e)}")

if __name__ == "__main__":
    main()