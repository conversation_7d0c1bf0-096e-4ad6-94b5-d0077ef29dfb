from device_utils import connect_device
import task_operations_tianmao
import task_tianmao_huayuan
import argparse

def main():
    # 设置命令行参数解析
    parser = argparse.ArgumentParser(description='执行设备任务')
    
    # 添加参数
    parser.add_argument('--sb_name', type=str, default='mix2_zi', help='设备名称')
    parser.add_argument('--sb_url', type=str, default='**************:5555', help='设备URL')
    parser.add_argument('--sb_url_params', type=str, default=None, help='设备URL参数')
    parser.add_argument('--need_open_app', action='store_true', help='是否需要在任务开始前回到桌面并打开应用')
    parser.add_argument('--is_clone', action='store_true', help='是否为克隆实例')
    parser.add_argument('--action', type=str, default='do_job', help='要执行的方法[U+FF08]如 do_job[U+FF09]')
    
    # 解析参数
    args = parser.parse_args()
    
    # 设备连接配置
    device_config = {
        "sb_name": args.sb_name,
        "sb_url": args.sb_url,
        "sb_url_params": args.sb_url_params
    }

    # 初始化连接
    try:
        connect_device(**device_config)
        
        # 根据 action 参数执行不同的方法
        if args.action == 'do_job':
            task_operations_tianmao.do_job(
                need_open_app=args.need_open_app,
                is_clone=args.is_clone
            )
        elif args.action == 'do_test':  
            task_operations_tianmao.do_test()
        elif args.action == 'do_ks':  
            task_operations_tianmao.do_ks()
        elif args.action == 'do_huayuan':  
            task_tianmao_huayuan.auto_drag_matching_elements_loop()
        else:
            print(f"未知的操作: {args.action}")
            
    except Exception as e:
        print(f"任务执行失败: {str(e)}")

if __name__ == "__main__":
    main()