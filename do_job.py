from device_utils import connect_device
import task_operations_tianmao
import task_tianmao_huayuan
import argparse
import importlib
import inspect

def execute_action(action_str, need_open_app=False, is_clone=False):
    """
    根据 action_str 动态执行指定模块的方法

    Args:
        action_str (str): 格式为 "模块名.方法名"，如 "task_operations_tianmao.do_job"
        need_open_app (bool): 是否需要打开应用
        is_clone (bool): 是否为克隆实例
    """
    try:
        # 解析模块名和方法名
        if '.' not in action_str:
            # 兼容旧格式，默认使用 task_operations_tianmao 模块
            module_name = 'task_operations_tianmao'
            method_name = action_str
            print(f"[兼容模式] 使用默认模块: {module_name}.{method_name}")
        else:
            module_name, method_name = action_str.rsplit('.', 1)

        print(f"[执行] 模块: {module_name}, 方法: {method_name}")

        # 动态导入模块
        try:
            module = importlib.import_module(module_name)
        except ImportError as e:
            print(f"[错误] 无法导入模块 '{module_name}': {str(e)}")
            return False

        # 检查方法是否存在
        if not hasattr(module, method_name):
            print(f"[错误] 模块 '{module_name}' 中不存在方法 '{method_name}'")
            return False

        # 获取方法对象
        method = getattr(module, method_name)

        # 检查是否为可调用对象
        if not callable(method):
            print(f"[错误] '{module_name}.{method_name}' 不是可调用的方法")
            return False

        # 获取方法签名，判断是否需要传递参数
        sig = inspect.signature(method)
        params = list(sig.parameters.keys())

        print(f"[信息] 方法参数: {params}")

        # 根据方法参数决定如何调用
        if 'need_open_app' in params and 'is_clone' in params:
            # 方法支持 need_open_app 和 is_clone 参数
            print(f"[调用] {module_name}.{method_name}(need_open_app={need_open_app}, is_clone={is_clone})")
            method(need_open_app=need_open_app, is_clone=is_clone)
        elif 'need_open_app' in params:
            # 方法只支持 need_open_app 参数
            print(f"[调用] {module_name}.{method_name}(need_open_app={need_open_app})")
            method(need_open_app=need_open_app)
        elif len(params) == 0:
            # 方法不需要参数
            print(f"[调用] {module_name}.{method_name}()")
            method()
        else:
            # 方法有其他参数，尝试无参数调用
            print(f"[调用] {module_name}.{method_name}() (忽略未知参数)")
            method()

        print(f"[成功] 方法 '{module_name}.{method_name}' 执行完成")
        return True

    except Exception as e:
        print(f"[错误] 执行方法时发生异常: {str(e)}")
        return False

def main():
    # 设置命令行参数解析
    parser = argparse.ArgumentParser(description='执行设备任务')
    
    # 添加参数
    parser.add_argument('--sb_name', type=str, default='mix2_zi', help='设备名称')
    parser.add_argument('--sb_url', type=str, default='**************:5555', help='设备URL')
    parser.add_argument('--sb_url_params', type=str, default=None, help='设备URL参数')
    parser.add_argument('--need_open_app', action='store_true', help='是否需要在任务开始前回到桌面并打开应用')
    parser.add_argument('--is_clone', action='store_true', help='是否为克隆实例')
    parser.add_argument('--action', type=str, default='task_operations_tianmao.do_job', help='要执行的方法，格式：模块名.方法名（如 task_operations_tianmao.do_job）')
    
    # 解析参数
    args = parser.parse_args()
    
    # 设备连接配置
    device_config = {
        "sb_name": args.sb_name,
        "sb_url": args.sb_url,
        "sb_url_params": args.sb_url_params
    }

    # 初始化连接
    try:
        connect_device(**device_config)
        
        # 根据 action 参数动态执行方法
        execute_action(args.action, args.need_open_app, args.is_clone)
            
    except Exception as e:
        print(f"任务执行失败: {str(e)}")

if __name__ == "__main__":
    main()