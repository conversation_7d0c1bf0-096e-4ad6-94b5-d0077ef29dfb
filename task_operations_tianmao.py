# task_operations_tianmao.py
import time
import cv2
import numpy as np
import random
from device_utils import get_device, get_log_path,get_device_name
import os
import re

def open_tmall(open_clone=False):
    """
    最终稳定版天猫应用启动（适配您的测试结果）
    参数:
        open_clone (bool): 
            False - 打开原版（左侧图标，默认）
            True - 打开分身（右侧图标）
    返回:
        bool: 是否成功执行
    """
    d = get_device()
    # 获取有效的天猫图标（最多检查2个实例）
    valid_icons = []
    for i in range(2):  # 只检查0和1，因为实例2会报错
        try:
            icon = d(text="天猫", instance=i)
            if icon.exists:
                valid_icons.append({
                    "instance": i,
                    "position": icon.center(),
                    "object": icon
                })
        except:
            continue

    # 根据找到的图标数量处理
    if len(valid_icons) == 0:
        print("[错误] 未找到任何天猫应用")
        return False

    # 按X坐标排序（从左到右）
    sorted_icons = sorted(valid_icons, key=lambda x: x["position"][0])

    # 单图标情况
    if len(sorted_icons) == 1:
        if not open_clone:
            try:
                sorted_icons[0]["object"].click()
                print(f"[成功] 已点击天猫原版（坐标: {sorted_icons[0]['position']}）")
                return True
            except:
                print("[错误] 点击图标失败")
                return False
        print("[错误] 要求打开分身但未找到第二个图标")
        return False

    # 双图标情况
    try:
        target = sorted_icons[1] if open_clone else sorted_icons[0]
        target["object"].click()
        print(
            f"[成功] 已点击天猫{'分身' if open_clone else '原版'}（坐标: {target['position']}）")
        return True
    except:
        print("[错误] 点击图标失败")
        return False
    

def random_swipe(duration_sec=40, interval_sec=2, x_radius=30, 
                y_radius=300, min_swipe_distance=100):
    """
    每隔指定时间在屏幕中心附近随机滑动，持续指定时间

    :param duration_sec: 总持续时间(秒)，默认40秒
    :param interval_sec: 滑动间隔时间(秒)，默认2秒
    :param radius: 中心点附近的像素范围
    :param min_swipe_distance: 最小滑动距离（Y 轴），默认100
    """
    d = get_device()
    # 获取设备屏幕尺寸
    screen_info = d.info
    screen_width = screen_info['displayWidth']
    screen_height = screen_info['displayHeight']
    width, height = screen_width, screen_height
    # 计算中心点坐标
    center_x = width // 2
    center_y = height // 2
    # 计算需要执行的滑动次数
    iterations = int(duration_sec / interval_sec)

    for i in range(iterations):
        # 生成 X 轴随机坐标（限制在中心附近）
        start_x = random.randint(center_x - x_radius, center_x + x_radius)
        end_x = random.randint(center_x - x_radius, center_x + x_radius)

        # 生成 Y 轴随机坐标，并确保滑动距离 ≥ min_swipe_distance
        while True:
            start_y = random.randint(center_y - y_radius, center_y + y_radius)
            end_y = random.randint(center_y - y_radius, center_y + y_radius)
            if abs(start_y - end_y) >= min_swipe_distance:
                break  # 确保滑动距离足够

        # 确保坐标不超出屏幕范围
        start_x = max(0, min(start_x, width - 1))
        start_y = max(0, min(start_y, height - 1))
        end_x = max(0, min(end_x, width - 1))
        end_y = max(0, min(end_y, height - 1))

        # 执行滑动操作
        d.swipe(start_x, start_y, end_x, end_y)

        # 打印当前进度
        print(f"已完成 {i+1}/{iterations} 次滑动，等待下一次...")

        # 如果不是最后一次，则等待间隔时间
        if i < iterations - 1:
            time.sleep(interval_sec)

        # 判断是否完成提前结束
        if find_and_click("返回领奖", click=False, screenshot=False) is not None or find_and_click("任务已完成快去领奖吧", click=False, screenshot=False) is not None:
            print("提示返回了，操作完成")
            break
    print("随机滑动操作完成")

def single_swipe_zfb(d,direction="random"):
    """
    从屏幕中心附近开始，按指定方向快速滑动一次（适中距离+随机值）
    
    :param direction: 滑动方向，可选 "up"(向上)、"down"(向下) 或 "random"(随机，默认)
    """

    # 获取设备屏幕尺寸
    screen_info = d.info
    screen_width = screen_info['displayWidth']
    screen_height = screen_info['displayHeight']
    width, height = screen_width, screen_height
    
    # 计算中心点坐标
    center_x = width // 2
    center_y = height // 2
    
    # 设置滑动参数
    x_radius = 30  # X轴随机偏移范围
    base_distance = 500  # 基础滑动距离
    random_offset = 150  # 随机偏移范围
    
    # 生成起始点（中心点附近）
    start_x = random.randint(center_x - x_radius, center_x + x_radius)
    start_y = random.randint(center_y - 50, center_y + 50)  # 在中心点上下50像素内
    
    # 计算实际滑动距离（基础距离±随机值）
    actual_distance = base_distance + random.randint(-random_offset, random_offset)
    
    # 根据参数决定滑动方向
    if direction.lower() == "up":
        end_y = start_y - actual_distance
    elif direction.lower() == "down":
        end_y = start_y + actual_distance
    elif direction.lower() == "random":
        end_y = start_y + actual_distance if random.choice([True, False]) else start_y - actual_distance
    else:
        raise ValueError("direction 参数必须是 'up'、'down' 或 'random'")
    
    # 确保坐标不超出屏幕范围
    start_x = max(0, min(start_x, width - 1))
    start_y = max(0, min(start_y, height - 1))
    end_x = start_x  # 保持X轴不变
    end_y = max(100, min(end_y, height - 100))  # 保留上下100像素的边界
    
    # 执行滑动操作（duration参数控制滑动速度，越小越快）
    d.swipe(start_x, start_y, end_x, end_y, duration=0.2)
    
    direction_str = "向上" if end_y < start_y else "向下"
    print(f"单次{direction_str}滑动完成，距离：{abs(start_y - end_y)}像素")

def single_swipe_ks(d,direction="random"):
    """
    从屏幕中心附近开始，按指定方向快速滑动一次（适中距离+随机值）
    
    :param direction: 滑动方向，可选 "up"(向上)、"down"(向下) 或 "random"(随机，默认)
    """

    # 获取设备屏幕尺寸
    screen_info = d.info
    screen_width = screen_info['displayWidth']
    screen_height = screen_info['displayHeight']
    width, height = screen_width, screen_height
    
    # 计算中心点坐标
    center_x = width // 2
    center_y = height // 2
    
    # 设置滑动参数
    x_radius = 50  # X轴随机偏移范围
    base_distance = 600  # 基础滑动距离
    random_offset = 150  # 随机偏移范围
    
    # 生成起始点（中心点附近）
    start_x = random.randint(center_x - x_radius, center_x + x_radius)
    start_y = random.randint(center_y - 50, center_y + 50)  # 在中心点上下50像素内
    
    # 计算实际滑动距离（基础距离±随机值）
    actual_distance = base_distance + random.randint(-random_offset, random_offset)
    
    # 根据参数决定滑动方向
    if direction.lower() == "up":
        end_y = start_y - actual_distance
    elif direction.lower() == "down":
        end_y = start_y + actual_distance
    elif direction.lower() == "random":
        end_y = start_y + actual_distance if random.choice([True, False]) else start_y - actual_distance
    else:
        raise ValueError("direction 参数必须是 'up'、'down' 或 'random'")
    
    # 确保坐标不超出屏幕范围
    start_x = max(0, min(start_x, width - 1))
    start_y = max(0, min(start_y, height - 1))
    end_x = start_x  # 保持X轴不变
    end_y = max(100, min(end_y, height - 100))  # 保留上下100像素的边界
    
    # 执行滑动操作（duration参数控制滑动速度，越小越快）
    d.swipe(start_x, start_y, end_x, end_y, duration=0.10)
    
    direction_str = "向上" if end_y < start_y else "向下"
    print(f"单次{direction_str}滑动完成，距离：{abs(start_y - end_y)}像素")    


def find_and_click(target_name, match_type='文本', pianyi_x=0, pianyi_y=0,
                   threshold=0.7, click=True, wait_after_click=2, screenshot=True):
    """
    通过指定方式定位元素并点击（可选）

    参数:
        target_name (str): 图片名称/文本内容/XPath表达式
        match_type (str): 匹配方式，必须为'图片'/'文本'/'xpath'
        pianyi_x (int): X轴点击偏移量（仅图片匹配时有效）
        pianyi_y (int): Y轴点击偏移量（仅图片匹配时有效）
        threshold (float): 图片匹配阈值(0-1)
        click (bool): 是否执行点击操作
        wait_after_click (int): 点击后等待时间（秒）
        screenshot (bool): 是否保存截图

    返回:
        bool: 图片匹配时返回是否找到
        object: 文本/xpath匹配时返回元素对象（找不到返回None）
    """

    d = get_device()
    log_path = get_log_path()

    def sanitize_filename(name):
        """清理文件名中的非法字符并限制长度"""
        # 替换非法字符为下划线
        clean_name = re.sub(r'[\\/*?:"<>|]', '_', name)
        # 移除首尾空白字符
        clean_name = clean_name.strip()
        # 限制文件名长度（Windows最大255，保留扩展名空间）
        return clean_name[:200] if len(clean_name) > 200 else clean_name

    if match_type not in ['图片', '文本', 'xpath']:
        raise ValueError("match_type 参数必须为 '图片'/'文本'/'xpath'")

    screenshot_name = f"after_find_and_click_{sanitize_filename(target_name)}.png"

    element = None

    try:
        # 图片匹配分支
        if match_type == '图片':
            target_img_path = target_name + ".png"
            if not os.path.exists(target_img_path):
                print(f"[错误] 图片文件不存在: {target_img_path}")
                return False

            screen_pil = d.screenshot()
            screen_cv = cv2.cvtColor(np.array(screen_pil), cv2.COLOR_RGB2BGR)

            template_img = cv2.imdecode(np.fromfile(
                target_img_path, dtype=np.uint8), cv2.IMREAD_COLOR)
            h, w = template_img.shape[:2]

            res = cv2.matchTemplate(
                screen_cv, template_img, cv2.TM_CCOEFF_NORMED)
            min_val, max_val, min_loc, max_loc = cv2.minMaxLoc(res)

            if max_val >= threshold:
                center_x = max_loc[0] + w // 2
                center_y = max_loc[1] + h // 2
                print(f"[成功] 图片匹配成功 [{target_name}] 置信度: {max_val:.2f}")

                if click:
                    click_x = center_x + pianyi_x
                    click_y = center_y + pianyi_y
                    d.click(click_x, click_y)
                    print(f"[点击] 已点击坐标: ({click_x}, {click_y})")
                    time.sleep(wait_after_click)

                return True  # 图片匹配始终返回布尔值
            return False

        # 文本匹配分支
        elif match_type == '文本':
            element = d(text=target_name)

        # XPath匹配分支
        elif match_type == 'xpath':
            element = d.xpath(target_name)

        # 处理元素查找结果
        if element.exists:
            print(f"[成功] {match_type}匹配成功 [{target_name}]")
            if click:
                element.click()
                print(f"[点击] 已点击{match_type}元素")
                time.sleep(wait_after_click)
            return element  # 返回元素对象
        return None  # 找不到返回None

    except Exception as e:
        print(f"[错误] 操作异常: {str(e)}")
        return False if match_type == '图片' else None

    finally:
        if screenshot:
            try:
                d.screenshot(os.path.join(log_path, screenshot_name))
                print(f"[截图] 已保存操作后截图: {screenshot_name}")
            except Exception as e:
                print(f"[错误] 截图保存失败: {str(e)}")


def do_job(need_open_app=False, is_clone=False):
    """
        执行自动化任务
        
        Args:
            need_open_app (bool): 是否需要在任务开始前回到桌面并打开应用
                - True: 自动回到桌面并启动应用（适用于应用未打开的情况）
                - False: 假设应用已在任务界面打开（跳过启动步骤）
            is_clone (bool): 标识操作的是否为分身应用
                - True: 操作分身/双开版应用
                - False: 操作原始/官方版应用
        
        Example:
            >>> # 操作原版应用，自动启动场景 "mix2", "xrqlovewsw.vip:8889"
            >>> do_job(need_open_app=True, is_clone=False)
            
            >>> # 操作分身应用，手动已启动场景 "k80pro", "**************:36123"
            >>> do_job(need_open_app=False)
        """
    # 方法实现开始...
    d = get_device()
    log_path = get_log_path()
    sb_name = get_device_name()

    #是否需要打开app
    if need_open_app :
        # 回到主页
        d.press("home")
        # 等待主页加载
        time.sleep(1)
        #打开app 原版或分身
        open_tmall(open_clone=is_clone)

        # 先按几次返回再打开确保在app首页
        for i in range(3):
            d.press("back")
            time.sleep(1)

        # 回到主页
        d.press("home")
        # 等待主页加载
        time.sleep(1)
        #打开app 原版或分身
        open_tmall(open_clone=is_clone)


        find_and_click("我")
        find_and_click("福气红包",wait_after_click=5)

    #在任务界面开始。。

    find_and_click("昨日任务")
    find_and_click("昨日任务")

    if find_and_click("福福福发福呦") is not None:
        print("进入了福福福发福呦")
        if find_and_click("兑红包红包天天上新",click=False)  is None:
            print("没找到兑红包红包天天上新,返回一下")
            d.press("back")
            time.sleep(2)
            find_and_click("福福福发福呦")


    find_and_click("今日签到")

    # find_and_click("任务交叉", match_type='图片')
    #任务交叉
    find_and_click("O1CN01eVRrEP1UBQnatGxTV_!!6000000002479-2-tps-96-96.png_")

    # 攒福气 
    if find_and_click('(//*[contains(@resource-id, "task-bubble")])[last()]/following-sibling::*[3]', match_type='xpath') is not None:

        # 开始循环做任务
        while True:
            press_back_time = 2

            # XPath表达式
            xpath = '//*[ contains(@text,"领200福气") \
            or @text="点商品，可领100福气" \
            or @text="点商品，可领50福气"  \
            or @text="搜索浏览商品，领50福气" \
            or @text="浏览30s,最高得50福气" \
            ]'

            if find_and_click(xpath, match_type='xpath', click=False) is not None:
                # 下一个节点是任务按钮
                if find_and_click(xpath+'/following-sibling::*', match_type='xpath') is not None:
                    # 还在当前界面说明是领取，继续点做任务
                    if find_and_click("今天做任务 明天领福气") is not None:
                        continue

                    # 进入任务界面
                    if find_and_click("搜索", click=False) is not None:
                        ele = find_and_click(
                            '//*[@text="搜索"]/..//android.widget.EditText', match_type='xpath')
                        if ele:
                        #     #zte手机的set_text不行，改剪切板方式
                        #     if sb_name == 'zte':
                        #         d.set_clipboard("防汛沙袋")
                        #         ele.click()
                        #         ele.long_click()
                        #         if d(text="粘贴").exists:
                        #             d(text="粘贴").click()
                        #         else:
                        #             # 备用方案：模拟键盘粘贴（Ctrl+V）
                        #             d.press("ctrl", "v")  # 仅支持部分键盘
                        #     else:
                        #         ele.set_text("防汛沙袋")
                        #         time.sleep(2)
                        #     find_and_click("搜索", click=True)
                            #历史搜索 
                            find_and_click(
                            '//*[@text="历史搜索"]/following-sibling::*//android.view.View', match_type='xpath')
                            #猜你想搜
                            find_and_click(
                            '//*[@text="猜你想搜"]/following-sibling::*[2]//android.view.View', match_type='xpath')
                        else:
                            print("未找到搜索输入框")

                    else:
                        print("没有搜索")

                    random_swipe()
                    # 返回,最多press_back_time 2次，到了任务也就停
                    for i in range(press_back_time):
                        if not find_and_click("今天做任务 明天领福气"):
                            d.press("back")
                            time.sleep(1)
            else:
                print("没有找到任务")
                break

        find_and_click("领取")

def run_swipe_loop(d):
    """循环执行滑动操作：在10个视频范围内随机上下滑动"""
    max_videos = 10  # 最大加载视频数
    current_position = 0  # 当前位置（0-9）
    swipe_count = 0
    is_guanggao = False
    
    while True:
        try:
            # 随机决定滑动方向（但在边界位置时强制反向）
            if current_position <= 0:
                direction = "down"  # 已经在顶部，只能向下
            elif current_position >= max_videos - 1:
                direction = "up"    # 已经在底部，只能向上
            else:
                direction = random.choice(["up", "down"])
            
            # 更新当前位置
            if direction == "down":
                current_position = min(current_position + 1, max_videos - 1)
            else:
                current_position = max(current_position - 1, 0)
            
            # 执行滑动并判断是否结束
            found_tomorrow = find_and_click("明日可领", click=False)
            found_tomorrow_again = find_and_click("明天再来", click=False) if found_tomorrow is None else None

            if found_tomorrow is None and found_tomorrow_again is None:
                # 两种情况都未找到，执行滑动
                single_swipe_zfb(d, direction=direction)
                swipe_count += 1
            elif found_tomorrow_again is not None:
                # 找到"明天再来"的情况
                print("找到明天再来，返回")
                d.press("back")
                time.sleep(1)

                

                if find_and_click("去看看") is not None:
                    is_guanggao = True
                    single_swipe_zfb(d, direction=direction)
                    swipe_count += 1
                else:
                    print("没有找到去看看，结束了")
                    break
            else:
                # 找到"明日可领"的情况
                find_and_click("明日可领")
                
                find_and_click("去预约")

                if find_and_click("去看看") is not None:
                    is_guanggao = True
                    single_swipe_zfb(d, direction=direction)
                    swipe_count += 1
                else:
                    print("没有找到去看看，结束了")
                    break

            # 如果找不到红包金额，可能是进入了直播间，退出一下
            if find_and_click("//*[@resource-id='com.alipay.android.living.dynamic:id/actionTv']", match_type='xpath', click=False) is None:
                print("可能进入了直播间，退出一下")
                d.press("back")
                time.sleep(1)
            
            # 打印当前滑动统计
            print(f"\n已执行 {swipe_count} 次滑动（当前方向：{direction}）")
            print(f"当前位置：{current_position + 1}/{max_videos}")
            
            if is_guanggao:
                # 随机等待15 - 20秒（带0.1秒精度）
                wait_time = round(random.uniform(15, 20), 1)
                print(f"等待 {wait_time} 秒后继续...")
                time.sleep(wait_time)
            else:
                # 随机等待5 - 10秒（带0.1秒精度）
                wait_time = round(random.uniform(5, 10), 1)
                print(f"等待 {wait_time} 秒后继续...")
                time.sleep(wait_time)
            
        except Exception as e:
            print(f"执行出错: {str(e)}")
            time.sleep(5)  # 出错后等待5秒再重试

def run_swipe_loop2(d):
    """循环执行滑动操作：大体5次向下 + 1次向上，带随机性"""
    swipe_count = 0
    down_counter = 0
    
    while True:
        try:
            # 带随机性的方向决策（大体保持5:1比例）
            if down_counter < random.randint(4, 6):  # 4-6次向下后才会向上
                direction = "up"
                down_counter += 1
            else:
                direction = "down"
                down_counter = 0  # 重置向下计数器
            
            # # 执行滑动 判断是否结束
            # if find_and_click("明天再来",click=False) is None and find_and_click("明日可领",click=False) is None :
            single_swipe_ks(d,direction=direction)
            swipe_count += 1
            # else:
            #     print("结束了")
            #     break

            # # 如果找不到红包金额，可能是进入了直播间，退出一下
            # if find_and_click("//*[@resource-id='com.alipay.android.living.dynamic:id/actionTv']",match_type='xpath',click=False) is None:
            #     print("可能进入了直播间，退出一下")
            #     d.press("back")
                # time.sleep(1)
            
            # 打印当前滑动统计
            print(f"\n已执行 {swipe_count} 次滑动（当前方向：{direction}）")
            print(f"向下滑动计数：{down_counter}/4-6")
            
            # 随机等待5 - 10秒（带0.1秒精度）
            wait_time = round(random.uniform(5, 10), 1)
            print(f"等待 {wait_time} 秒后继续...")
            time.sleep(wait_time)
            
        except Exception as e:
            print(f"执行出错: {str(e)}")
            time.sleep(5)  # 出错后等待5秒再重试

def do_test():
    
    d = get_device()
    
    run_swipe_loop(d)

def do_ks():
    
    d = get_device()
    
    # run_swipe_loop2(d)
    # find_and_click("领取奖励")
    # find_and_click("去赚钱")

    # find_and_click("//*[@text='看6次直播领金币']/../..//*[@text='领福利']",match_type='xpath')

    # find_and_click("//*[@resource-id='com.kuaishou.nebula:id/play_view_container']",match_type='xpath')
    #代表有金币计时
    #//*[@resource-id="com.kuaishou.nebula:id/neo_count_down_image_group"] 
    #等一分钟，返回


    ##################################

    # find_and_click("//*[@text='看指定视频赚金币']/../..//*[@text='去观看']",match_type='xpath')

    #循环滑动

    ##############################

    # find_and_click("//*[@text='刷广告视频赚金币']/../..//*[@text='领福利']",match_type='xpath')

    # #循环滑动
    # run_swipe_loop2(d)

    #########################
    # 看广告得金币

    while True:
        # 任务开始
        if find_and_click("//*[@text='看广告得金币']/../..//*[@text='领福利']", match_type='xpath') is not None:
            print("找到并点击了'领福利'按钮")
            
            # 每2秒判断一次是否成功领取
            while True:
                time.sleep(2)
                
                # 检查是否已成功领取
                if find_and_click("//*[contains(@text,'已成功领取')]", match_type='xpath', click=False) is not None:
                    print("检测到'已成功领取'，返回")
                    d.press('back')
                    time.sleep(2)
                    
                    # 处理返回后的三种情况
                    # 情况1：直接返回到任务页（继续外层循环）
                    if find_and_click("//*[@text='看广告得金币']/../..//*[@text='领福利']", match_type='xpath', click=False):
                        print("已返回任务页")
                        break  # 跳出内层循环，继续外层循环
                    
                    # 情况2：弹窗 - 领取奖励
                    elif find_and_click("领取奖励"):
                        print("点击了'领取奖励'，继续观看下一个视频")
                        time.sleep(3)
                        # 继续内层循环（继续检查是否已成功领取）
                        continue
                    
                    # 情况3：弹窗 - 放弃奖励
                    elif find_and_click("放弃奖励"):
                        print("点击了'放弃奖励'，返回任务页")
                        time.sleep(3)
                        break  # 跳出内层循环，继续外层循环
                    
                    # 其他未知情况
                    else:
                        print("未知状态，尝试等待")
                        # d.press('back')
                        time.sleep(2)
                        break
                else:
                    element = d.xpath("//*[contains(@text,'后可领取')]")
                    if element.exists:
                        text = element.get_text()
                        print(text)
                    if find_and_click("拒绝") is not None:
                        print("拒绝弹出app")
                        d.press('back')
                        time.sleep(2)
                        d.press('back')
                        time.sleep(2)

    ##############################
    